/**
 * Design Tokens - Single Source of Truth
 * 
 * This file contains all design tokens for the application.
 * Modify these values to update the entire design system.
 * 
 * Color Format: OKLCH (Perceptually uniform color space)
 * - L: Lightness (0-1)
 * - C: Chroma (saturation, 0-0.4)
 * - H: <PERSON><PERSON> (0-360 degrees)
 */

:root {
  /* === LIGHT THEME === */
  
  /* Base Colors */
  --background: oklch(1.0000 0 0);                    /* Pure white */
  --foreground: oklch(0.3211 0 0);                    /* Dark gray text */
  
  /* Surface Colors */
  --card: oklch(1.0000 0 0);                          /* White cards */
  --card-foreground: oklch(0.3211 0 0);               /* Dark text on cards */
  --popover: oklch(1.0000 0 0);                       /* White popovers */
  --popover-foreground: oklch(0.3211 0 0);            /* Dark text on popovers */
  
  /* Brand Colors */
  --primary: oklch(0.6412 0.1155 155.0013);           /* Teal primary */
  --primary-foreground: oklch(1.0000 0 0);            /* White text on primary */
  --secondary: oklch(0.4375 0.0929 159.3902);         /* Darker teal secondary */
  --secondary-foreground: oklch(1.0000 0 0);          /* White text on secondary */
  
  /* Neutral Colors */
  --muted: oklch(0.9166 0.0148 102.4717);             /* Light gray backgrounds */
  --muted-foreground: oklch(0.5382 0 0);              /* Medium gray text */
  --accent: oklch(0.6412 0.1155 155.0013);             /* Green accent */
  --accent-foreground: oklch(1.0000 0 0);             /* White text on accent */
  
  /* Semantic Colors */
  --destructive: oklch(0.6766 0.1260 25.1211);        /* Red for errors/danger */
  --destructive-foreground: oklch(1.0000 0 0);        /* White text on destructive */
  
  /* Border & Input Colors */
  --border: oklch(0.8699 0 0);                        /* Light gray borders */
  --input: oklch(0.8699 0 0);                         /* Input field borders */
  --ring: oklch(0.6412 0.1155 155.0013);              /* Focus ring color */
  
  /* Chart Colors */
  --chart-1: oklch(0.6412 0.1155 155.0013);           /* Primary chart color */
  --chart-2: oklch(0.7196 0.0906 267.0774);           /* Purple chart color */
  --chart-3: oklch(0.8118 0.0701 218.3524);           /* Blue chart color */
  --chart-4: oklch(0.6019 0.0723 251.0410);           /* Indigo chart color */
  --chart-5: oklch(0.5737 0.1247 152.5238);           /* Green chart color */
  
  /* Sidebar Colors */
  --sidebar: oklch(1.0000 0 0);                       /* White sidebar */
  --sidebar-foreground: oklch(0.3211 0 0);            /* Dark sidebar text */
  --sidebar-primary: oklch(0.6412 0.1155 155.0013);   /* Primary sidebar elements */
  --sidebar-primary-foreground: oklch(1.0000 0 0);    /* White text on sidebar primary */
  --sidebar-accent: oklch(1.0000 0 0);                /* Accent sidebar elements */
  --sidebar-accent-foreground: oklch(0.3211 0 0);     /* Dark text on sidebar accent */
  --sidebar-border: oklch(0.8699 0 0);                /* Sidebar borders */
  --sidebar-ring: oklch(0.6412 0.1155 155.0013);      /* Sidebar focus rings */
  
  /* Typography */
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  
  /* Border Radius */
  --radius: 0.5rem;
  
  /* Shadows */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  
  /* Spacing & Typography */
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  /* === DARK THEME === */
  
  /* Base Colors */
  --background: oklch(0.2393 0.0100 268.2607);        /* Dark blue background */
  --foreground: oklch(0.9219 0 0);                    /* Light gray text */
  
  /* Surface Colors */
  --card: oklch(0.3337 0.0065 229.0172);              /* Dark blue cards */
  --card-foreground: oklch(0.9219 0 0);               /* Light text on cards */
  --popover: oklch(0.3337 0.0065 229.0172);           /* Dark blue popovers */
  --popover-foreground: oklch(0.9219 0 0);            /* Light text on popovers */
  
  /* Brand Colors */
  --primary: oklch(0.6412 0.1155 155.0013);           /* Same teal primary */
  --primary-foreground: oklch(1.0000 0 0);            /* White text on primary */
  --secondary: oklch(0.6019 0.0723 251.0410);         /* Purple secondary */
  --secondary-foreground: oklch(0.9219 0 0);          /* Light text on secondary */
  
  /* Neutral Colors */
  --muted: oklch(0.3867 0 0);                         /* Dark gray backgrounds */
  --muted-foreground: oklch(0.7155 0 0);              /* Light gray text */
  --accent: oklch(0.6412 0.1155 155.0013);            /* Green accent */
  --accent-foreground: oklch(1.0000 0 0);             /* White text on accent */
  
  /* Semantic Colors */
  --destructive: oklch(0.6766 0.1260 25.1211);        /* Same red for errors */
  --destructive-foreground: oklch(1.0000 0 0);        /* White text on destructive */
  
  /* Border & Input Colors */
  --border: oklch(0.3867 0 0);                        /* Dark gray borders */
  --input: oklch(0.3867 0 0);                         /* Dark input fields */
  --ring: oklch(0.6412 0.1155 155.0013);              /* Same focus ring */
  
  /* Chart Colors */
  --chart-1: oklch(0.6412 0.1155 155.0013);           /* Primary chart color */
  --chart-2: oklch(0.6019 0.0723 251.0410);           /* Purple chart color */
  --chart-3: oklch(0.7196 0.0906 267.0774);           /* Blue chart color */
  --chart-4: oklch(0.8118 0.0701 218.3524);           /* Light blue chart color */
  --chart-5: oklch(0.5737 0.1247 152.5238);           /* Green chart color */
  
  /* Sidebar Colors */
  --sidebar: oklch(0.2393 0.0100 268.2607);           /* Dark sidebar */
  --sidebar-foreground: oklch(0.9219 0 0);            /* Light sidebar text */
  --sidebar-primary: oklch(0.6412 0.1155 155.0013);   /* Primary sidebar elements */
  --sidebar-primary-foreground: oklch(1.0000 0 0);    /* White text on sidebar primary */
  --sidebar-accent: oklch(0.7196 0.0906 267.0774);    /* Purple sidebar accent */
  --sidebar-accent-foreground: oklch(0.9219 0 0);     /* Light text on sidebar accent */
  --sidebar-border: oklch(0.3867 0 0);                /* Dark sidebar borders */
  --sidebar-ring: oklch(0.6412 0.1155 155.0013);      /* Sidebar focus rings */
  
  /* Typography (same as light) */
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  
  /* Border Radius (same as light) */
  --radius: 0.5rem;
  
  /* Shadows (same as light) */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

/**
 * DESIGN SYSTEM NOTES:
 * 
 * 1. Color Palette:
 *    - Primary: Teal (155° hue) - Professional, trustworthy
 *    - Secondary: Purple (251° hue) - Creative, modern
 *    - Accent: Yellow/Purple (72°/267° hue) - Energetic, attention-grabbing
 *    - Destructive: Red (25° hue) - Clear danger indication
 * 
 * 2. Typography Scale:
 *    - Sans: Plus Jakarta Sans (modern, readable)
 *    - Serif: Source Serif 4 (elegant, traditional)
 *    - Mono: JetBrains Mono (code, technical content)
 * 
 * 3. Spacing System:
 *    - Base unit: 0.25rem (4px)
 *    - Consistent 4px grid system
 * 
 * 4. Border Radius:
 *    - Base: 0.5rem (8px)
 *    - Variants: sm (4px), md (6px), lg (8px), xl (12px)
 * 
 * 5. Shadow System:
 *    - Subtle elevation with consistent opacity
 *    - Progressive depth from 2xs to 2xl
 * 
 * 6. Accessibility:
 *    - OKLCH ensures perceptual uniformity
 *    - High contrast ratios maintained
 *    - Focus rings clearly visible
 * 
 * To customize your design:
 * 1. Modify the OKLCH values above
 * 2. Adjust typography scale if needed
 * 3. Update spacing/radius for different feel
 * 4. Test in both light and dark modes
 */
