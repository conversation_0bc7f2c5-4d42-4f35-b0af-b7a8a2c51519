"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileText, Edit, BarChart3, Tag } from "lucide-react";

interface HowItWorksModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HowItWorksModal({ isOpen, onClose }: HowItWorksModalProps) {
  const tableFeatures = [
    {
      icon: FileText,
      title: "Title",
      description: "Main item name"
    },
    {
      icon: Edit,
      title: "Actions",
      description: "What was done"
    },
    {
      icon: BarChart3,
      title: "Results",
      description: "Outcomes achieved"
    },
    {
      icon: Tag,
      title: "Status",
      description: "Current state"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-4">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-center mb-4">
            Table Guide
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 px-2">
          {tableFeatures.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-200">
                  <IconComponent className="w-4 h-4 text-gray-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">{feature.title}</h3>
                  <p className="text-xs text-gray-600">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-6 p-3 bg-blue-50 rounded-lg mx-2">
          <p className="text-xs text-blue-800 text-center">
            Click cells to edit • Drag rows to reorder • Use + to add items
          </p>
        </div>

        <div className="flex justify-center mt-4 pb-2">
          <Button onClick={onClose} size="sm" className="px-6">
            Got it
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
