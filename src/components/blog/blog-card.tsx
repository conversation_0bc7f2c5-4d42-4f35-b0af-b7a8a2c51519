"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BlogCardProps } from "@/types/blog";
import { Calendar, Clock } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export function BlogCard({ post, className = "" }: BlogCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Link href={`/blog/${post.slug}`} className={`block ${className}`}>
      <Card className="bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer border h-full flex flex-col p-2">
        {post.image && (
          <div className="relative w-full h-48 overflow-hidden rounded-t-lg">
            <Image
              src={post.image}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-200 hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              unoptimized
            />
            {post.featured && (
              <Badge className="absolute top-3 left-3 bg-[#166534] hover:bg-[#166534]/90">
                Featured
              </Badge>
            )}
          </div>
        )}
        
        <CardHeader className="pb-3">
          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{post.readTime} min read</span>
            </div>
          </div>

          <CardTitle className="text-lg font-semibold line-clamp-2 leading-tight">
            {post.title}
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-0 flex-1">
          <CardDescription className="text-sm leading-relaxed line-clamp-3">
            {post.description}
          </CardDescription>
           
        </CardContent>
      </Card>
    </Link>
  );
}
