import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import React, { useEffect, useRef, useState } from "react";

interface VerificationCodeInputProps {
  label?: string;
  length?: number;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

export const VerificationCodeInput = React.forwardRef<
  HTMLDivElement,
  VerificationCodeInputProps
>(
  (
    {
      label = "Verification Code",
      length = 6,
      value = "",
      onChange,
      error,
      disabled = false,
      className,
    },
    ref
  ) => {
    const [codes, setCodes] = useState<string[]>(
      Array(length)
        .fill("")
        .map((_, i) => value[i] || "")
    );
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

    useEffect(() => {
      const newCodes = Array(length)
        .fill("")
        .map((_, i) => value[i] || "");
      setCodes(newCodes);
    }, [value, length]);

    const handleChange = (index: number, newValue: string) => {
      // Only allow digits
      if (newValue && !/^\d$/.test(newValue)) return;

      const newCodes = [...codes];
      newCodes[index] = newValue;
      setCodes(newCodes);

      // Call onChange with the complete code
      onChange?.(newCodes.join(""));

      // Auto-focus next input
      if (newValue && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    };

    const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
      if (e.key === "Backspace" && !codes[index] && index > 0) {
        // Focus previous input on backspace if current is empty
        inputRefs.current[index - 1]?.focus();
      } else if (e.key === "ArrowLeft" && index > 0) {
        inputRefs.current[index - 1]?.focus();
      } else if (e.key === "ArrowRight" && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    };

    const handlePaste = (e: React.ClipboardEvent) => {
      e.preventDefault();
      const pastedData = e.clipboardData.getData("text").replace(/\D/g, "");
      const newCodes = Array(length).fill("");

      for (let i = 0; i < Math.min(pastedData.length, length); i++) {
        newCodes[i] = pastedData[i];
      }

      setCodes(newCodes);
      onChange?.(newCodes.join(""));

      // Focus the next empty input or the last input
      const nextEmptyIndex = newCodes.findIndex((code) => !code);
      const focusIndex = nextEmptyIndex === -1 ? length - 1 : nextEmptyIndex;
      inputRefs.current[focusIndex]?.focus();
    };

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {label && <Label>{label}</Label>}
        <div className="flex gap-2 justify-center">
          {codes.map((code, index) => (
            <Input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={code}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              disabled={disabled}
              className={cn(
                "w-12 h-12 text-center text-lg font-semibold",
                error && "border-destructive",
                "focus:ring-2 focus:ring-primary focus:border-primary"
              )}
              aria-label={`Digit ${index + 1} of ${length}`}
            />
          ))}
        </div>
        {error && (
          <p className="text-sm text-destructive text-center">{error}</p>
        )}
      </div>
    );
  }
);

VerificationCodeInput.displayName = "VerificationCodeInput";
