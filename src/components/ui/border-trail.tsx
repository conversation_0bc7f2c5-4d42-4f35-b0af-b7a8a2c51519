"use client";
import { cn } from "@/lib/utils";
import { motion, Transition } from "framer-motion";

type BorderTrailProps = {
  className?: string;
  size?: number;
  transition?: Transition;
  delay?: number;
  onAnimationComplete?: () => void;
  style?: React.CSSProperties;
};

export function BorderTrail({
  className,
  size = 60,
  transition,
  delay,
  onAnimationComplete,
  style,
}: BorderTrailProps) {
  const BASE_TRANSITION: Transition = {
    repeat: Infinity,
    duration: 5,
    ease: "linear",
  };

  return (
    <div className="pointer-events-none absolute inset-0 rounded-[inherit] overflow-hidden">
      <motion.div
        className={cn("absolute", className)}
        style={{
          width: size,
          height: size,
          ...style,
        }}
        animate={{
          x: ["0%", "calc(100% - 60px)", "calc(100% - 60px)", "0%", "0%"],
          y: ["0%", "0%", "calc(100% - 60px)", "calc(100% - 60px)", "0%"],
        }}
        transition={{
          ...(transition ?? BASE_TRANSITION),
          delay: delay,
          times: [0, 0.25, 0.5, 0.75, 1],
        }}
        onAnimationComplete={onAnimationComplete}
      />
    </div>
  );
}
