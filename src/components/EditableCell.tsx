"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";

interface EditableCellProps {
  id: string;
  field: keyof BusinessItemDetail;
  value: string;
  multiline?: boolean;
  className?: string;
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
  newRowData?: any;
}

export function EditableCell({
  id,
  field,
  value,
  multiline = false,
  className = "",
  editingCell,
  setEditingCell,
  onSave,
  newRowData
}: EditableCellProps) {
  const [localValue, setLocalValue] = useState(value);
  const isEditing = editingCell?.id === id && editingCell?.field === field;
  const isNewRow = id === 'new-row';

  // For new row, use newRowData, otherwise use the passed value
  const currentValue = isNewRow ? newRowData?.[field as keyof typeof newRowData] || '' : value;

  // Update local value when prop value changes
  useEffect(() => {
    const valueToUse = isNewRow ? newRowData?.[field as keyof typeof newRowData] || '' : value;
    setLocalValue(valueToUse);
  }, [value, isNewRow, newRowData, field]);

  const handleEdit = () => {
    setEditingCell({ id, field });
    const valueToUse = isNewRow ? newRowData?.[field as keyof typeof newRowData] || '' : value;
    setLocalValue(valueToUse);
  };

  const handleSaveAndExit = () => {
    onSave(id, field, localValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveAndExit();
    } else if (e.key === 'Escape') {
      const valueToUse = isNewRow ? newRowData?.[field as keyof typeof newRowData] || '' : value;
      setLocalValue(valueToUse);
      setEditingCell(null);
    }
  };

  if (isEditing) {
    if (multiline) {
      return (
        <Textarea
          value={localValue}
          onChange={(e) => setLocalValue(e.target.value)}
          onBlur={handleSaveAndExit}
          onKeyDown={handleKeyDown}
          className="min-h-[80px] resize-none"
          autoFocus
        />
      );
    } else {
      return (
        <Input
          value={localValue}
          onChange={(e) => setLocalValue(e.target.value)}
          onBlur={handleSaveAndExit}
          onKeyDown={handleKeyDown}
          className="h-8"
          autoFocus
        />
      );
    }
  }

  const displayValue = currentValue || (isNewRow ? 'Click to add...' : 'Click to edit...');

  return (
    <div 
      className={`cursor-pointer hover:bg-muted/50 transition-colors rounded p-2 -m-2 ${className} ${
        isNewRow ? 'text-muted-foreground italic' : ''
      }`}
      onClick={handleEdit}
      title="Click to edit"
    >
      <div className="whitespace-pre-wrap break-words">
        {displayValue}
      </div>
    </div>
  );
} 