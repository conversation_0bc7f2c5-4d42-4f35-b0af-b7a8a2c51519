"use client";

import { FullScreenLoading } from "@/components/ui/app-loading";
import { useSessionStore } from "@/stores/sessionStore";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

export function ProtectedRoute({
  children,
  redirectTo = "/",
  requireAuth = true,
}: ProtectedRouteProps) {
  const { isAuthenticated, isInitialized, user } = useSessionStore();
  const router = useRouter();

  useEffect(() => {
    if (isInitialized) {
      if (requireAuth && !isAuthenticated) {
        // Redirect to login if authentication is required but user is not authenticated
        router.replace(redirectTo);
      } else if (!requireAuth && isAuthenticated) {
        // Redirect directly to appropriate dashboard based on user role
        if (user?.role === "admin") {
          router.replace("/admin");
        } else {
          router.replace("/user-dashboard");
        }
      }
    }
  }, [
    isAuthenticated,
    isInitialized,
    requireAuth,
    redirectTo,
    router,
    user?.role,
  ]);

  // Show loading while checking authentication
  if (!isInitialized) {
    return <FullScreenLoading message="Checking authentication..." />;
  }

  // Don't render children if redirecting
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  if (!requireAuth && isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}

// Convenience components for different auth scenarios
export function RequireAuth({
  children,
  redirectTo,
}: {
  children: React.ReactNode;
  redirectTo?: string;
}) {
  return (
    <ProtectedRoute requireAuth={true} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  );
}

export function RequireGuest({
  children,
  redirectTo,
}: {
  children: React.ReactNode;
  redirectTo?: string;
}) {
  return (
    <ProtectedRoute requireAuth={false} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  );
}
