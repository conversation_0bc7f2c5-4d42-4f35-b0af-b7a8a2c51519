"use client";

import { FullScreenLoading } from "@/components/ui/app-loading";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: "admin" | "user";
  fallback?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  requiredRole,
  fallback,
}: ProtectedRouteProps) {
  const { isSignedIn, isLoaded, user } = useClerkAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push("/");
    }
  }, [isSignedIn, isLoaded, router]);

  // Show loading state
  if (!isLoaded) {
    return <FullScreenLoading message="Loading..." />;
  }

  // Not authenticated
  if (!isSignedIn) {
    return fallback || null;
  }

  // For now, skip role checking since Clerk users don't have roles by default
  // You can add role checking later if needed
  if (requiredRole && false) { // Temporarily disabled
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-2">
            Access Denied
          </h1>
          <p className="text-muted-foreground">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
