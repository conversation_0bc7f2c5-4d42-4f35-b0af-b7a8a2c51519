"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  ChevronUp,
  Lightbulb,
  Zap,
  CheckCircle,
  MessageCircle,
  Building,
  Target,
  Users,
  TrendingUp,
  Package,
  BarChart3,
  DollarSign,
  Shield,
  AlertTriangle,
  Star,
  Briefcase,
  Palette,
  Megaphone,
  FileText,
  Settings,
  Globe,
  ShoppingCart,
  Heart,
  Clock,
  Check,
} from "lucide-react";
import { useState } from "react";
import type { BusinessItem, BusinessSection } from "@/types/BusinessSection.types";
import { ICON_SIZES } from "@/lib/constants";

// Item Row Component
const ItemRow = ({ item, onItemClick }: { item: BusinessItem, onItemClick: (item: BusinessItem) => void }) => {
  const Icon = item.icon;
  
  const getStatusStyles = (status: string) => {
    switch (status) {
      case "active":
        return "bg-gray-200 dark:bg-background text-gray-900 dark:text-gray-100";
      case "completed":
        return "bg-gray-200 dark:bg-background text-gray-900 dark:text-gray-100";
      case "pending":
        return "bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300";
      case "disabled":
        return "bg-gray-50 dark:bg-background text-gray-400 dark:text-gray-500";
      default:
        return "bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300";
    }
  };
 

  return (
    <div
      className={`flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] ${getStatusStyles(item.status)} hover:bg-primary/10 dark:hover:bg-primary/20 border border-border/50 dark:border-border/30 p-0`}
      onClick={() => {
        // Handle item click - you can add navigation or modal here
        console.log(`Clicked on ${item.title}`);
        onItemClick(item);
      }}
    >
      <div className="flex items-center gap-3">
        {/* <Icon className="h-5 w-5" /> */}
        <span className="font-medium text-sm">{item.title}</span>
      </div>
      <div className="flex items-center gap-1">
        {item.status !== "unproven" && (
          <>
            {item.actions > 0 && (
              <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.25 border border-blue-200 dark:border-blue-700 h-6">
                <Zap className="h-3 w-3" />
                {item.actions > 1 ? item.actions : ''}
              </Badge>
            )}
            {item.ideas > 0 && (
              <Badge variant="secondary" className="bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.25 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6">
                <Lightbulb className="h-3 w-3" />
                {item.ideas > 1 ? item.ideas : ''}
              </Badge>
            )}
            {item.results > 0 && (
              <Badge variant="secondary" className="bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.25 border border-green-700 dark:border-green-600 h-6">
                <Check className="h-4 w-6" />
               {item.results > 1 ? item.results : ''}
              </Badge>
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Expandable Card Component
const ExpandableCard = ({ section, onItemClick }: { section: BusinessSection, onItemClick: (item: BusinessItem) => void }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <Card className="bg-white dark:bg-card border border-gray-200 dark:border-border shadow-sm h-fit py-0">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {section.title}
              </CardTitle>
              <ChevronUp
                className={`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform ${
                  isExpanded ? "rotate-0" : "rotate-180"
                }`}
              />
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="py-0 px-3">
            {section.items.map((item) => (
              <ItemRow key={item.id} item={item} onItemClick={onItemClick} />
            ))}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

interface BusinessSectionsGridProps {
  sections: BusinessSection[];
  onItemClick: (item: BusinessItem) => void;
}

export function BusinessSectionsGrid({ sections, onItemClick }: BusinessSectionsGridProps) {
  return (
    <div className="w-full">
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 auto-rows-min">
          {sections.map((section) => (
            <ExpandableCard key={section.id} section={section} onItemClick={onItemClick} />
          ))}
        </div>
      </div>
    </div>
  );
} 