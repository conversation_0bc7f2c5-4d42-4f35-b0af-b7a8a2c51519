"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { BusinessSection, BusinessItem } from "@/types/BusinessSection.types";

interface BusinessSectionsGridProps {
  sections: BusinessSection[];
  onItemClick: (item: BusinessItem) => void;
}

export function BusinessSectionsGrid({ sections, onItemClick }: BusinessSectionsGridProps) {
  const getStatusColor = (status: BusinessItem["status"]) => {
    switch (status) {
      case "idea":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "action":
        return "bg-orange-100 text-orange-800 border-orange-300";
      case "confirmed":
        return "bg-green-100 text-green-800 border-green-300";
      case "unproven":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getStatusText = (status: Business<PERSON>tem["status"]) => {
    switch (status) {
      case "idea":
        return "Idea";
      case "action":
        return "In Action";
      case "confirmed":
        return "Confirmed";
      case "unproven":
        return "Unproven";
      default:
        return "Unknown";
    }
  };

  const getStatusDescription = (status: BusinessItem["status"]) => {
    switch (status) {
      case "idea":
        return "Initial concept stage";
      case "action":
        return "Being implemented";
      case "confirmed":
        return "Results validated";
      case "unproven":
        return "Results inconclusive";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-8">
      {sections.map((section) => (
        <div key={section.id} className="space-y-4">
          <h2 className="text-2xl font-bold text-foreground">{section.title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {section.items.map((item) => {
              const Icon = item.icon;
              return (
                <Card
                  key={item.id}
                  className="hover:shadow-lg transition-all duration-200 cursor-pointer border-2 hover:border-primary/20"
                  onClick={() => onItemClick(item)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-sm font-semibold leading-tight">
                            {item.title}
                          </CardTitle>
                        </div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <Badge 
                        className={`${getStatusColor(item.status)} text-xs font-medium px-2 py-1`}
                        title={getStatusDescription(item.status)}
                      >
                        {getStatusText(item.status)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="grid grid-cols-3 gap-3 text-sm">
                      <div className="text-center">
                        <div className="font-semibold text-lg text-blue-600">{item.ideas}</div>
                        <div className="text-xs text-muted-foreground">Ideas</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-lg text-orange-600">{item.actions}</div>
                        <div className="text-xs text-muted-foreground">Actions</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-lg text-green-600">{item.results}</div>
                        <div className="text-xs text-muted-foreground">Results</div>
                      </div>
                    </div>
                    {item.status !== "idea" && (
                      <div className="mt-4">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full text-xs h-8"
                          onClick={(e) => {
                            e.stopPropagation();
                            onItemClick(item);
                          }}
                        >
                          View Details
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
} 