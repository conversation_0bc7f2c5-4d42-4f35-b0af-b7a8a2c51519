"use client";

import { use<PERSON><PERSON>k<PERSON><PERSON> } from "@/hooks/useClerk<PERSON>pi";
import { useAnalytics } from "@/hooks/useAnalytics";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { toast } from "sonner";

/**
 * Example component showing how to use Clerk tokens for authenticated API calls
 * This demonstrates the proper way to get access tokens when using Clerk
 * Also includes PostHog analytics tracking examples
 */
export function ClerkApiExample() {
  const { get, post, put, isSignedIn, getToken } = useClerkApi();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState<any>(null);

  // Example: Get current user data from backend
  const fetchUserData = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in first");
      return;
    }

    trackClick("fetch-user-data", "api-example");
    setLoading(true);
    try {
      const data = await get("/api/auth/me"); // This will use Clerk token automatically
      setUserData(data);
      trackCustomEvent("user_data_fetched", { success: true });
      toast.success("User data fetched successfully");
    } catch (error) {
      console.error("Error fetching user data:", error);
      trackCustomEvent("user_data_fetched", { success: false, error: error instanceof Error ? error.message : String(error) });
      toast.error("Failed to fetch user data");
    } finally {
      setLoading(false);
    }
  };

  // Example: Create a new project
  const createProject = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in first");
      return;
    }

    setLoading(true);
    try {
      const projectData = {
        name: "Test Project",
        description: "A test project created via API",
        status: "active"
      };

      const result = await post("/api/projects", projectData);
      toast.success("Project created successfully");
      console.log("Created project:", result);
    } catch (error) {
      console.error("Error creating project:", error);
      toast.error("Failed to create project");
    } finally {
      setLoading(false);
    }
  };

  // Example: Update user profile
  const updateProfile = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in first");
      return;
    }

    setLoading(true);
    try {
      const updateData = {
        bio: "Updated via API",
        preferences: {
          notifications: true,
          theme: "dark"
        }
      };

      const result = await put("/api/auth/me", updateData);
      toast.success("Profile updated successfully");
      console.log("Updated profile:", result);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setLoading(false);
    }
  };

  // Example: Get raw token for manual use
  const showToken = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in first");
      return;
    }

    try {
      const token = await getToken();
      console.log("Current Clerk token:", token);
      toast.success("Token logged to console");
    } catch (error) {
      console.error("Error getting token:", error);
      toast.error("Failed to get token");
    }
  };

  if (!isSignedIn) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Clerk API Example</CardTitle>
          <CardDescription>Please sign in to test API calls</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Clerk API Example</CardTitle>
        <CardDescription>
          Examples of making authenticated API calls using Clerk tokens
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Button 
            onClick={fetchUserData} 
            disabled={loading}
            variant="outline"
          >
            Fetch User Data
          </Button>
          
          <Button 
            onClick={createProject} 
            disabled={loading}
            variant="outline"
          >
            Create Project
          </Button>
          
          <Button 
            onClick={updateProfile} 
            disabled={loading}
            variant="outline"
          >
            Update Profile
          </Button>
          
          <Button 
            onClick={showToken} 
            disabled={loading}
            variant="outline"
          >
            Show Token
          </Button>
        </div>

        {userData && (
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <h4 className="font-semibold mb-2">User Data:</h4>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(userData, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
          <h4 className="font-semibold mb-2">How it works:</h4>
          <ul className="text-sm space-y-1">
            <li>• useClerkApi hook automatically gets Clerk JWT token</li>
            <li>• Token is included in Authorization header</li>
            <li>• Backend validates the Clerk token</li>
            <li>• No need to manage token storage manually</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
