"use client";

import { FolderOpen, Home, User } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";

import { NavPlatform } from "@/components/nav-platform";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/hooks/useAuth";

// User dashboard navigation data
const getUserNavData = (user: any) => ({
  user: {
    name: user?.name || "User",
    email: user?.email || "",
    avatar: user?.avatar || "",
  },
  teams: [
    {
      name: "Siift",
      logo: Home,
      plan: "Personal",
    },
  ],
  projects: [
    {
      name: "Website Redesign",
      url: "/projects/website-redesign",
      icon: FolderOpen,
    },
    {
      name: "Mobile App",
      url: "/projects/mobile-app",
      icon: FolderOpen,
    },
    {
      name: "API Documentation",
      url: "/projects/api-docs",
      icon: FolderOpen,
    },
    {
      name: "Marketing Campaign",
      url: "/projects/marketing",
      icon: FolderOpen,
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/user-dashboard",
      icon: Home,
      isActive: true,
    },
  ],
  platform: [
    {
      title: "Actions",
      url: "/user-dashboard?tab=actions",
      icon: User, // Will be Zap icon
      badge: "3",
    },
    {
      title: "Ideas",
      url: "/user-dashboard?tab=ideas",
      icon: User, // Will be Lightbulb icon
    },
    {
      title: "Todo",
      url: "/user-dashboard?tab=todo",
      icon: User, // Will be CheckSquare icon
    },
    {
      title: "Calendar",
      url: "/user-dashboard?tab=calendar",
      icon: User, // Will be Calendar icon
    },
  ],
});

export function UserSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { user, logout } = useAuth();
  const router = useRouter();

  const data = getUserNavData(user);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        {/* <NavMain items={data.navMain} /> */}
        <NavPlatform items={data.platform} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
