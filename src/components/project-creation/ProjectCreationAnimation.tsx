"use client";

import { Logo } from "@/components/ui/logo";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { motion, AnimatePresence } from "framer-motion";

import { useEffect } from "react";

export function ProjectCreationAnimation() {
  const {
    currentStep,
    currentProgress,
    totalSteps,
    completedSteps,
    showLogo,
    logoRotating,
    showGlow,
    circleExpanding,
    showCompletionMessage,
    createdProject,
    setCircleExpanding,
    setShowCompletionMessage,
  } = useProjectCreationStore();

  // Lock body scroll during project creation
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Handle the expansion animation when project is completed
  useEffect(() => {
    if (currentStep === 'completed') {
      console.log('Project completed, starting animations...');
      // Start logo expansion immediately when project completes
      setCircleExpanding(true);

      // Show completion message after logo expansion
      const timer = setTimeout(() => {
        console.log('Showing completion message...');
        setShowCompletionMessage(true);
      }, 2000); // Wait for logo expansion to complete

      return () => clearTimeout(timer);
    }
  }, [currentStep, setCircleExpanding, setShowCompletionMessage]);

  if (currentStep === 'idle') {
    return null;
  }

  // Debug logging
  console.log('Animation state:', {
    currentStep,
    circleExpanding,
    showCompletionMessage,
    createdProject
  });

  return (
    <div className="fixed inset-0 z-50 bg-background overflow-hidden">

      {/* Noise Texture Overlay */}
      <div
        className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: "256px 256px",
        }}
      />

      {/* Expanding Circle Animation */}
      <AnimatePresence>
        {circleExpanding && !showCompletionMessage && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 150 }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
            className="fixed top-1/2 left-1/2 w-4 h-4 bg-[#166534] rounded-full -translate-x-1/2 -translate-y-1/2 z-40"
            style={{ transformOrigin: "center" }}
          />
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="relative z-30 flex flex-col items-center justify-center min-h-screen p-8">
        {!showCompletionMessage ? (
          <>
            {/* Logo with Glow Effect */}
            <AnimatePresence>
              {showLogo && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5 }}
                  className="relative mb-12"
                >
                  {/* Glow Effect */}
                  {showGlow && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="absolute inset-0 -m-8"
                    >
                      <div className="absolute inset-0 bg-[#166534]/20 rounded-full blur-3xl animate-pulse" />
                      <div className="absolute inset-0 bg-[#22c55e]/10 rounded-full blur-2xl animate-pulse" />
                    </motion.div>
                  )}
                  
                  {/* Logo */}
                  <div className="relative z-10">
                    <Logo size={120} animated={logoRotating} />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Progress Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="w-full max-w-md space-y-4"
            >
              <h2 className="text-2xl font-bold text-center text-foreground mb-8">
                Creating Your Project
              </h2>

              {/* Current Progress Item */}
              <div className="space-y-3">
                <AnimatePresence mode="wait">
                  {currentProgress ? (
                    <motion.div
                      key={currentProgress.id}
                      initial={{ opacity: 0, x: -20, scale: 0.95 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      exit={{ opacity: 0, x: 20, scale: 0.95 }}
                      transition={{ duration: 0.4, ease: "easeOut" }}
                      className={`flex items-center gap-4 p-6 rounded-xl backdrop-blur border-2 transition-all duration-300 ${
                        currentProgress.completed
                          ? "bg-[#166534]/10 border-[#166534]/30 shadow-lg shadow-[#166534]/20"
                          : "bg-card/50 border-border"
                      }`}
                    >
                      <div className="flex-shrink-0">
                        {currentProgress.completed ? (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{
                              type: "spring",
                              stiffness: 500,
                              damping: 30,
                              delay: 0.1
                            }}
                            className="w-7 h-7 bg-[#166534] rounded-full flex items-center justify-center"
                          >
                            <svg
                              className="w-4 h-4 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </motion.div>
                        ) : (
                          <div className="w-7 h-7 border-2 border-muted-foreground rounded-full animate-pulse" />
                        )}
                      </div>
                      <span
                        className={`text-lg font-medium transition-colors duration-300 ${
                          currentProgress.completed
                            ? "text-[#166534] font-semibold"
                            : "text-foreground"
                        }`}
                      >
                        {currentProgress.message}
                      </span>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex items-center justify-center p-6 rounded-xl bg-card/30 backdrop-blur border border-dashed"
                    >
                      <span className="text-muted-foreground">Initializing project creation...</span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Overall Progress Bar */}
              <div className="mt-6">
                <div className="flex justify-between text-sm text-muted-foreground mb-2">
                  <span>Progress</span>
                  <span>
                    {completedSteps} / {totalSteps}
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{
                      width: `${
                        totalSteps > 0
                          ? (completedSteps / totalSteps) * 100
                          : 0
                      }%`,
                    }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="bg-[#166534] h-2 rounded-full"
                  />
                </div>
              </div>
            </motion.div>
          </>
        ) : (
          /* Completion Message */
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="fixed inset-0 z-[60] overflow-hidden"
          >
            {/* Gradient Background */}
            <div className="absolute inset-0 z-0 bg-gradient-to-br from-[#166534] via-[#22c55e] to-[#16a34a]" />

            {/* Background Effects */}
            <div className="absolute inset-0 z-10">
              {/* Custom dots for green background */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {Array.from({ length: 200 }, (_, i) => (
                  <div
                    key={i}
                    className="absolute rounded-full"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      width: `${Math.random() * 3 + 1}px`,
                      height: `${Math.random() * 3 + 1}px`,
                      backgroundColor: Math.random() > 0.5 ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.2)',
                      opacity: Math.random() * 0.6 + 0.2,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Noise Texture Overlay */}
            <div
              className="absolute inset-0 z-20 opacity-[0.08]"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
                backgroundSize: "256px 256px",
              }}
            />

            {/* Additional Pattern Overlay */}
            <div
              className="absolute inset-0 z-30 opacity-[0.05]"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='grid' width='10' height='10' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 10 0 L 0 0 0 10' fill='none' stroke='white' stroke-width='0.5' opacity='0.3'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grid)'/%3E%3C/svg%3E")`,
                backgroundSize: "50px 50px",
              }}
            />

            {/* Content */}
            <div className="relative z-40 flex flex-col items-center justify-center min-h-screen p-8 text-center space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.8, ease: "easeOut" }}
              >
                <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg">
                  YAY! Project is ready
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 drop-shadow-md">
                  Let's dive in
                </p>
              </motion.div>

              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-[#166534] px-8 py-4 rounded-xl font-bold text-lg hover:bg-white/90 transition-all duration-200 shadow-lg"
                onClick={() => {
                  window.location.href = '/user-dashboard';
                }}
              >
                Continue to Dashboard
              </motion.button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
