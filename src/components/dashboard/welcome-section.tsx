"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { FolderOpen, Plus } from "lucide-react";
import Link from "next/link";

export function WelcomeSection() {
  const { user } = useAuth();

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  return (
    <Card className="bg-gradient-to-r from-primary/10 via-primary/5 to-background border-primary/20">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              {getGreeting()}, {user?.name}! 👋
            </h1>
            <p className="text-muted-foreground">
              Welcome back to your project dashboard. Ready to build something
              amazing?
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/user-dashboard">
                <Plus className="mr-2 h-4 w-4" />
                New Project
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/projects">
                <FolderOpen className="mr-2 h-4 w-4" />
                View All Projects
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
