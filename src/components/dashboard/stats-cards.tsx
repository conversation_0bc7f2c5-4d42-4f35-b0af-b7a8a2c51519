'use client';

import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { FolderOpen, CheckCircle, Clock, Archive } from 'lucide-react';

interface StatsCardsProps {
  stats: {
    totalProjects: number;
    activeProjects: number;
    completedProjects: number;
    archivedProjects: number;
  };
}

export function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      title: 'Total Projects',
      value: stats.totalProjects,
      icon: FolderOpen,
      description: 'All projects in your workspace',
    },
    {
      title: 'Active Projects',
      value: stats.activeProjects,
      icon: Clock,
      description: 'Currently in progress',
    },
    {
      title: 'Completed Projects',
      value: stats.completedProjects,
      icon: CheckCircle,
      description: 'Successfully finished',
    },
    {
      title: 'Archived Projects',
      value: stats.archivedProjects,
      icon: Archive,
      description: 'Moved to archive',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card) => {
        const Icon = card.icon;
        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
