"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";
import { useAuth } from "@/hooks/useAuth";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { useProjectCreationStream } from "@/lib/mockEventStream";
import { ArrowRight, <PERSON>rkles } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export function DashboardHeroSection() {
  const { user } = useAuth();
  const router = useRouter();
  const [currentHintIndex, setCurrentHintIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState("");
  const [isTyping, setIsTyping] = useState(true);
  const [ideaInput, setIdeaInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Project creation store
  const {
    setIdeaText,
    startCreation,
    setCurrentProgress,
    setTotalSteps,
    incrementCompletedSteps,
    setCurrentStep,
    setCreatedProject,
    currentStep,
  } = useProjectCreationStore();

  // Event stream hook
  const { startCreation: startStream, addEventListener, removeEventListener } = useProjectCreationStream();

  const hints = [
    "manages team tasks and deadlines...",
    "tracks project progress and milestones...",
    "automates workflow and notifications...",
    "analyzes business metrics and insights...",
    "coordinates team collaboration..."
  ];

  useEffect(() => {
    const currentHint = hints[currentHintIndex];
    let timeoutId: NodeJS.Timeout;

    if (isTyping) {
      // Typing effect
      if (displayedText.length < currentHint.length) {
        timeoutId = setTimeout(() => {
          setDisplayedText(currentHint.slice(0, displayedText.length + 1));
        }, 100); // Typing speed
      } else {
        // Finished typing, wait before starting to delete
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, 2000); // Pause duration
      }
    } else {
      // Deleting effect
      if (displayedText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayedText(displayedText.slice(0, -1));
        }, 50); // Deleting speed (faster than typing)
      } else {
        // Finished deleting, move to next hint
        setCurrentHintIndex((prev) => (prev + 1) % hints.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayedText, isTyping, currentHintIndex, hints]);

  // Set up event stream listener
  useEffect(() => {
    const handleStreamEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;

      switch (type) {
        case 'setup':
          setTotalSteps(data.totalSteps);
          break;
        case 'progress':
          setCurrentProgress(data);
          break;
        case 'step_complete':
          incrementCompletedSteps();
          break;
        case 'complete':
          setCreatedProject({
            id: data.projectId,
            name: data.name,
            description: data.description,
          });
          setCurrentStep('completed');
          setIsSubmitting(false);
          break;
        case 'error':
          console.error('Project creation error:', data.error);
          setIsSubmitting(false);
          break;
      }
    };

    addEventListener(handleStreamEvent);
    return () => removeEventListener(handleStreamEvent);
  }, [addEventListener, removeEventListener, setCurrentProgress, setTotalSteps, incrementCompletedSteps, setCreatedProject, setCurrentStep]);

  const handleCreateProject = () => {
    if (!ideaInput.trim()) return;

    setIsSubmitting(true);
    setIdeaText(ideaInput);
    startCreation();
    startStream(ideaInput);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  return (
    <div className="relative z-10 container mx-auto px-4 text-center py-20 md:py-20 flex-1 flex items-center">
      <div className="w-full">
        {/* Welcome Message */}
        <div className="mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Badge variant="secondary" className="bg-[#166534]/10 text-[#166534] border-[#166534]/20">
              <Sparkles className="w-3 h-3 mr-1" />
              Welcome back
            </Badge>
          </div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-4 leading-tight">
            {getGreeting()}, {user?.name}!
          </h1>
          <p className="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl mx-auto">
            Ready to turn your ideas into reality? Let's build something amazing together.
          </p>
        </div>

        {/* CTA Input Area */}
        <div className="max-w-2xl mx-auto">
          <div className="group relative bg-card/80 backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden">
            {/* Subtle gradient overlay on the card */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 pointer-events-none" />
            
            <div className="relative z-10">
              <div className="flex items-center gap-2 mb-4">
                <Logo size={24} animated={false} />
                <span className="text-sm font-medium text-muted-foreground">
                  Describe your next project idea
                </span>
              </div>

              <div className="relative mt-4">
                <div className="relative">
                  <textarea
                    value={ideaInput}
                    onChange={(e) => setIdeaInput(e.target.value)}
                    placeholder={displayedText}
                    className="w-full px-4 py-3 pr-16 rounded-lg border bg-background/50 backdrop-blur text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-[#166534]/20 focus:border-[#166534] resize-none min-h-[80px] transition-all duration-300 hover:shadow-lg hover:shadow-[#166534]/20 relative z-10"
                    rows={3}
                    disabled={isSubmitting}
                  />
                  <Button
                    onClick={handleCreateProject}
                    disabled={!ideaInput.trim() || isSubmitting}
                    className="absolute right-3 bottom-4 bg-[#166534] hover:bg-[#166534]/90 text-white shadow-lg z-20 disabled:opacity-50 disabled:cursor-not-allowed"
                    size="icon"
                  >
                    {isSubmitting ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <ArrowRight className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground mt-4">
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  AI-powered insights
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                  Smart project planning
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                  Team collaboration
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
