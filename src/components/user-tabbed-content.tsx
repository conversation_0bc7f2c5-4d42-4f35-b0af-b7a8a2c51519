"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  Bell,
  Settings,
  User,
  FolderOpen,
  Clock,
  Plus,
  Edit,
  Star,
  Calendar,
  Users,
  Activity,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react";

// Mock data for user dashboard tabs
const mockUserProfile = {
  name: "<PERSON>",
  email: "<EMAIL>",
  role: "User",
  joinDate: "March 2024",
  lastLogin: "1 hour ago",
  avatar: "",
  stats: {
    projectsCreated: 8,
    tasksCompleted: 45,
    collaborations: 12,
    achievements: 6
  }
};

const mockUserNotifications = [
  {
    id: 1,
    title: "Project Update",
    message: "Your project 'Website Redesign' has been updated",
    type: "info",
    time: "10 minutes ago",
    read: false
  },
  {
    id: 2,
    title: "Task Completed",
    message: "Task 'Design Homepage' has been marked as complete",
    type: "success",
    time: "2 hours ago",
    read: false
  },
  {
    id: 3,
    title: "Collaboration Request",
    message: "Sarah invited you to collaborate on 'Mobile App'",
    type: "info",
    time: "1 day ago",
    read: true
  },
  {
    id: 4,
    title: "Deadline Reminder",
    message: "Project deadline approaching in 3 days",
    type: "warning",
    time: "2 days ago",
    read: true
  }
];

const mockUserProjects = [
  {
    id: 1,
    name: "Website Redesign",
    description: "Complete redesign of company website",
    status: "in-progress",
    progress: 75,
    dueDate: "2024-02-15",
    priority: "high",
    collaborators: 3
  },
  {
    id: 2,
    name: "Mobile App Development",
    description: "iOS and Android app development",
    status: "in-progress",
    progress: 45,
    dueDate: "2024-03-01",
    priority: "medium",
    collaborators: 5
  },
  {
    id: 3,
    name: "Marketing Campaign",
    description: "Q1 marketing campaign planning",
    status: "completed",
    progress: 100,
    dueDate: "2024-01-30",
    priority: "low",
    collaborators: 2
  },
  {
    id: 4,
    name: "Database Migration",
    description: "Migrate legacy database to new system",
    status: "planning",
    progress: 15,
    dueDate: "2024-04-15",
    priority: "high",
    collaborators: 1
  }
];

const mockUserRecentActivity = [
  {
    id: 1,
    action: "Created new project",
    details: "Website Redesign",
    timestamp: "30 minutes ago",
    type: "project"
  },
  {
    id: 2,
    action: "Completed task",
    details: "Design Homepage Layout",
    timestamp: "2 hours ago",
    type: "task"
  },
  {
    id: 3,
    action: "Joined collaboration",
    details: "Mobile App Development",
    timestamp: "1 day ago",
    type: "collaboration"
  },
  {
    id: 4,
    action: "Updated profile",
    details: "Changed profile picture",
    timestamp: "3 days ago",
    type: "profile"
  },
  {
    id: 5,
    action: "Shared project",
    details: "Marketing Campaign with team",
    timestamp: "1 week ago",
    type: "share"
  }
];

const mockUserSettings = {
  profile: {
    displayName: "John Doe",
    email: "<EMAIL>",
    timezone: "UTC-5 (Eastern)",
    language: "English"
  },
  preferences: {
    emailNotifications: true,
    pushNotifications: false,
    weeklyDigest: true,
    projectUpdates: true,
    theme: "System"
  },
  privacy: {
    profileVisibility: "Public",
    showEmail: false,
    showProjects: true,
    allowCollaborations: true
  }
};

interface UserTabbedContentProps {
  activeTab: string;
}

export function UserTabbedContent({ activeTab }: UserTabbedContentProps) {
  const router = useRouter();

  const getTabContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <div className="space-y-6">
            <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-6">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={mockUserProfile.avatar} />
                    <AvatarFallback className="text-lg">
                      {mockUserProfile.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold">{mockUserProfile.name}</h3>
                    <p className="text-muted-foreground">{mockUserProfile.email}</p>
                    <Badge variant="secondary">{mockUserProfile.role}</Badge>
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Joined</p>
                        <p className="font-medium">{mockUserProfile.joinDate}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Last Active</p>
                        <p className="font-medium">{mockUserProfile.lastLogin}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Your Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{mockUserProfile.stats.projectsCreated}</p>
                    <p className="text-sm text-muted-foreground">Projects Created</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{mockUserProfile.stats.tasksCompleted}</p>
                    <p className="text-sm text-muted-foreground">Tasks Completed</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">{mockUserProfile.stats.collaborations}</p>
                    <p className="text-sm text-muted-foreground">Collaborations</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600">{mockUserProfile.stats.achievements}</p>
                    <p className="text-sm text-muted-foreground">Achievements</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "settings":
        return (
          <div className="space-y-6">
            <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Account Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Display Name</p>
                    <p className="font-medium">{mockUserSettings.profile.displayName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{mockUserSettings.profile.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Timezone</p>
                    <p className="font-medium">{mockUserSettings.profile.timezone}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Language</p>
                    <p className="font-medium">{mockUserSettings.profile.language}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Email Notifications</p>
                    <Badge variant={mockUserSettings.preferences.emailNotifications ? "default" : "secondary"}>
                      {mockUserSettings.preferences.emailNotifications ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Push Notifications</p>
                    <Badge variant={mockUserSettings.preferences.pushNotifications ? "default" : "secondary"}>
                      {mockUserSettings.preferences.pushNotifications ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Weekly Digest</p>
                    <Badge variant={mockUserSettings.preferences.weeklyDigest ? "default" : "secondary"}>
                      {mockUserSettings.preferences.weeklyDigest ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Theme</p>
                    <p className="font-medium">{mockUserSettings.preferences.theme}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "notifications":
        return (
          <div className="space-y-6">
            <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Recent Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockUserNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 rounded-lg border ${
                        notification.read ? 'bg-muted/50' : 'bg-background'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{notification.title}</h4>
                            {!notification.read && (
                              <Badge variant="default" className="h-2 w-2 p-0 rounded-full" />
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{notification.message}</p>
                          <p className="text-xs text-muted-foreground">{notification.time}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          {notification.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
                          {notification.type === 'warning' && <AlertCircle className="h-4 w-4 text-yellow-600" />}
                          {notification.type === 'info' && <Info className="h-4 w-4 text-blue-600" />}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "projects":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <Card className="flex-1 mr-4 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FolderOpen className="h-5 w-5" />
                    My Projects
                  </CardTitle>
                </CardHeader>
              </Card>
              <Button
                className="flex items-center gap-2 border"
                onClick={() => router.push('/user-dashboard')}
              >
                <Plus className="h-4 w-4" />
                Create New Project
              </Button>
            </div>

            <div className="grid gap-4">
              {mockUserProjects.map((project) => (
                <Card key={project.id} className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{project.name}</h3>
                          <Badge 
                            variant={
                              project.status === 'in-progress' ? 'default' :
                              project.status === 'completed' ? 'secondary' : 'outline'
                            }
                          >
                            {project.status.replace('-', ' ')}
                          </Badge>
                          <Badge 
                            variant={
                              project.priority === 'high' ? 'destructive' :
                              project.priority === 'medium' ? 'default' : 'secondary'
                            }
                          >
                            {project.priority} priority
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{project.description}</p>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Progress</span>
                            <span>{project.progress}%</span>
                          </div>
                          <Progress value={project.progress} className="h-2" />
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            {project.collaborators} collaborators
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Due {project.dueDate}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Star className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case "recent":
        return (
          <div className="space-y-6">
            <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockUserRecentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg border">
                      <div className={`p-2 rounded-full ${
                        activity.type === 'project' ? 'bg-blue-100 text-blue-600' :
                        activity.type === 'task' ? 'bg-green-100 text-green-600' :
                        activity.type === 'collaboration' ? 'bg-purple-100 text-purple-600' :
                        activity.type === 'profile' ? 'bg-indigo-100 text-indigo-600' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {activity.type === 'project' ? <FolderOpen className="h-4 w-4" /> :
                         activity.type === 'task' ? <CheckCircle className="h-4 w-4" /> :
                         activity.type === 'collaboration' ? <Users className="h-4 w-4" /> :
                         activity.type === 'profile' ? <User className="h-4 w-4" /> :
                         <Activity className="h-4 w-4" />}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-muted-foreground">{activity.details}</p>
                      </div>
                      <p className="text-sm text-muted-foreground">{activity.timestamp}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <p className="text-muted-foreground">Select a tab to view content</p>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="space-y-6">
      {getTabContent()}
    </div>
  );
}
