"use client";

import { ProtectedRoute } from "@/components/auth/protected-route";
import { MainLayout } from "./main-layout";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <ProtectedRoute>
      <MainLayout showFooter={false} constrainHeight={true}>
        <div className="container mx-auto py-6 h-full overflow-y-auto">{children}</div>
      </MainLayout>
    </ProtectedRoute>
  );
}
