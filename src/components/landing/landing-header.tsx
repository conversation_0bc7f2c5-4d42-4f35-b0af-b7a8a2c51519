"use client";

import { ThemeToggle } from "@/components/theme-toggle";
import { Button } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import Link from "next/link";
import { useRouter } from "next/navigation";

export function LandingHeader() {
  const { isSignedIn, user } = useClerkAuth();
  const router = useRouter();

  const handleDashboardClick = () => {
    router.push("/user-dashboard");
  };

  return (
    <header className="w-full sticky top-0 z-50 backdrop-blur-md border-b border-white/10 dark:border-black/10 bg-[#cff5cf31] dark:bg-[#000000bb]">
      <div className="container mx-auto px-4 flex h-16 items-center justify-between relative z-10">
        {/* Left: Logo and App Title */}
        <Logo size={32} animated={false} showText={true} href="/" />

        {/* Right: Theme Toggle and Auth Buttons */}
        <div className="flex items-center gap-3">
          <ThemeToggle />

          {isSignedIn ? (
            <Button
              onClick={handleDashboardClick}
              className="bg-[#166534] hover:bg-[#166534]/90 text-white"
            >
              Dashboard
            </Button>
          ) : (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                asChild
                className="hover:bg-[#166534]/10 hover:text-[#166534]"
              >
                <Link href="/auth/login">Login</Link>
              </Button>
              <Button
                asChild
                className="bg-[#166534] hover:bg-[#166534]/90 text-white"
              >
                <Link href="/auth/register">Get Started</Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
