"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Star, 
  Users, 
  Globe, 
  Briefcase, 
  User, 
  ShoppingCart,
  Building,
  Palette
} from "lucide-react";

const communityProjects = [
  {
    title: "Personal website",
    description: "A modern portfolio website with dark mode and responsive design",
    category: "Personal",
    icon: User,
    color: "bg-blue-500",
    popular: true
  },
  {
    title: "Recipe finder",
    description: "Discover and save your favorite recipes with smart search",
    category: "Lifestyle", 
    icon: ShoppingCart,
    color: "bg-green-500",
    popular: false
  },
  {
    title: "3D product viewer",
    description: "Interactive 3D product showcase for e-commerce",
    category: "E-commerce",
    icon: Globe,
    color: "bg-purple-500",
    popular: true
  },
  {
    title: "Job board",
    description: "Connect talent with opportunities in your industry",
    category: "Business",
    icon: Briefcase,
    color: "bg-indigo-500",
    popular: false
  },
  {
    title: "Consumer App",
    description: "Mobile-first consumer application with real-time features",
    category: "Mobile",
    icon: Users,
    color: "bg-pink-500",
    popular: true
  },
  {
    title: "B2B App",
    description: "Enterprise solution for business process automation",
    category: "Enterprise",
    icon: Building,
    color: "bg-indigo-500",
    popular: false
  },
  {
    title: "Prototype",
    description: "Rapid prototyping tool for design validation",
    category: "Design",
    icon: Palette,
    color: "bg-yellow-500",
    popular: true
  }
];

const categories = [
  "Popular",
  "Discover", 
  "Internal Tools",
  "Website",
  "Personal",
  "Consumer App",
  "B2B App",
  "Prototype"
];

export function CommunitySection() {
  return (
    <section className="py-20 bg-card/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            From the Community
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover what others are building with Siift. Get inspired and start your own project.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-2 justify-center mb-12">
          {categories.map((category, index) => (
            <Badge
              key={index}
              variant={index === 0 ? "default" : "outline"}
              className={`cursor-pointer transition-all hover:scale-105 ${
                index === 0 
                  ? "bg-[#166534] text-white hover:bg-[#166534]/90" 
                  : "hover:bg-[#166534]/10 hover:border-[#166534] hover:text-[#166534]"
              }`}
            >
              {category}
            </Badge>
          ))}
        </div>

        {/* Project Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {communityProjects.map((project, index) => {
            const Icon = project.icon;
            return (
              <Card 
                key={index} 
                className="hover:shadow-lg transition-all duration-300 hover:scale-105 border-border/50 bg-card/80 backdrop-blur cursor-pointer group"
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className={`w-10 h-10 rounded-lg ${project.color} flex items-center justify-center`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    {project.popular && (
                      <Badge variant="secondary" className="text-xs">
                        <Star className="w-3 h-3 mr-1 fill-current" />
                        Popular
                      </Badge>
                    )}
                  </div>
                  <CardTitle className="text-lg font-semibold group-hover:text-[#166534] transition-colors">
                    {project.title}
                  </CardTitle>
                  <Badge variant="outline" className="w-fit text-xs">
                    {project.category}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed">
                    {project.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button 
            variant="outline" 
            className="hover:bg-[#166534]/10 hover:text-[#166534] hover:border-[#166534]"
          >
            View All Projects
          </Button>
        </div>
      </div>
    </section>
  );
}
