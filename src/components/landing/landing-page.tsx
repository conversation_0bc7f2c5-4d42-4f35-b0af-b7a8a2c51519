"use client";

import { Footer } from "@/components/layout/footer";
import { Testimonials } from "@/components/ui/testimonials";
import { WaitlistSection } from "@/components/ui/waitlist-section";
import { FeatureGrid } from "./feature-grid";
import { HeroSection } from "./hero-section";
import { BlogSection } from "@/components/blog/blog-section";
import { getLatestPosts } from "@/data/blog-posts";

export function LandingPage() {
  const latestPosts = getLatestPosts(6);

  return (
    <div className="min-h-screen flex flex-col bg-background relative">
      <main className="flex-1">
        <HeroSection />
        <FeatureGrid />
        <BlogSection posts={latestPosts} />
        <Testimonials />
        <WaitlistSection />
      </main>
      <Footer />
    </div>
  );
}
