"use client";

import { ProtectedRoute } from "@/components/auth/protected-route";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  SidebarInset,
  SidebarProvider,
  useSidebar,
} from "@/components/ui/sidebar";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { ArrowLeft, User, FileText, Plus, X, Zap, MoreVertical, Eye, HelpCircle } from "lucide-react";
import { ProjectSidebar } from "../../../components/project-sidebar";
import { BusinessSectionsGrid } from "../../../components/business-sections/BusinessSectionsGrid";
import { useBusinessSectionStore } from "../../../stores/businessSectionStore";
import { useBusinessItemStore } from "../../../stores/businessItemStore";
import { BusinessItemTable } from "../../../components/business-item-table";
import { fetchBusinessSections } from "../../../lib/businessSectionsData";
import { SidebarButton } from "../../../components/ui/sidebar-button";
import { ICON_SIZES } from "../../../lib/constants";
import { useResizable } from "../../../hooks/useResizable";
import { useAnalytics } from "../../../hooks/useAnalytics";
import { motion, AnimatePresence } from "framer-motion";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Mock action items data for priorities
const mockActionItems = [
  { id: 1, title: "Research competitors", status: "pending", priority: "high" },
  { id: 2, title: "Create wireframes", status: "in-progress", priority: "high" },
  { id: 3, title: "Set up analytics", status: "completed", priority: "medium" },
  { id: 4, title: "Design logo", status: "pending", priority: "medium" },
  { id: 5, title: "Write content", status: "pending", priority: "low" },
];

// Header component for detail view
function ProjectDetailHeader({
  selectedBusinessItem,
  onBackToItems
}: {
  selectedBusinessItem: any;
  onBackToItems: () => void;
}) {
  const { trackClick, trackCustomEvent } = useAnalytics();
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const { state, isMobile } = useSidebar();

  return (
    <header className="flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border">
      <div className="flex items-center justify-between w-full h-full px-4">
        {/* Left side - Back button on mobile, Item Title when sidebar is collapsed */}
        <div className="flex items-center gap-4 h-full">
          {state === "collapsed" && (
            <>
              <SidebarButton
                onClick={() => {
                  trackClick("back-to-items", "project-detail-header");
                  trackCustomEvent("navigation_clicked", {
                    destination: "items",
                    from_page: "item-detail",
                    location: "header"
                  });
                  onBackToItems();
                }}
                icon={ArrowLeft}
                variant="ghost"
                size="lg"
                layout="icon-only"
                showBorder={true}
                hoverColor="grey"
                hoverScale={true}
                iconClassName={ICON_SIZES.lg}
              />
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {selectedBusinessItem?.title || 'Untitled Item'}
              </h1>
            </>
          )}
        </div>

        {/* Right side - Help button with popover */}
        <div className="flex items-center gap-2 h-full">
          <Popover open={isHelpOpen} onOpenChange={setIsHelpOpen}>
            <PopoverTrigger>
              <SidebarButton
                icon={HelpCircle}
                variant="ghost"
                size="lg"
                layout="icon-only"
                showBorder={true}
                hoverColor="grey"
                hoverScale={true}
                onClick={() => {
                  trackClick("help-button", "project-header");
                  trackCustomEvent("help_clicked", {
                    from_item: selectedBusinessItem?.title,
                    location: "header"
                  });
                }}
                iconClassName={ICON_SIZES.lg}
              />
            </PopoverTrigger>
            <PopoverContent align="end" side="bottom" className="w-80">
              <div className="space-y-3">
                <h3 className="font-semibold text-sm">Table Guide</h3>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span><strong>Title:</strong> Main item name</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span><strong>Actions:</strong> What was done</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span><strong>Results:</strong> Outcomes achieved</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span><strong>Status:</strong> Current state</span>
                  </div>
                </div>
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Click cells to edit • Drag rows to reorder • Use + to add items
                  </p>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}

// Header component for main view (when no item is selected)
function ProjectHeader({
  activeContent,
  setActiveContent,
  mockDraftItems,
  mockFileItems,
  selectedBusinessItem,
  onBackToItems
}: {
  activeContent: 'drafts' | 'files' | null;
  setActiveContent: (content: 'drafts' | 'files' | null) => void;
  mockDraftItems: any[];
  mockFileItems: any[];
  selectedBusinessItem: any;
  onBackToItems: () => void;
}) {
  const router = useRouter();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const [isActionDropdownOpen, setIsActionDropdownOpen] = useState(false);
  const { state, isMobile } = useSidebar();

  // If an item is selected, show the detail header
  if (selectedBusinessItem) {
    return (
      <ProjectDetailHeader
        selectedBusinessItem={selectedBusinessItem}
        onBackToItems={onBackToItems}
      />
    );
  }

  return (
    <header className="flex flex-col h-20 shrink-0 transition-[width] ease-linear border-b border-border">
      <div className="flex items-center w-full flex-1 px-4 gap-4">

        {/* Progress bar - fills full width */}
        <div className="flex-1">
          {state === "collapsed" ? (
            /* Mobile/Compact version - just a number button matching SidebarButton height */
            <SidebarButton
              text="30%"
              variant="ghost"
              size="lg"
              layout="horizontal"
              hoverColor="grey"
              hoverScale={true}
              showBorder={true}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300 border"
            />
          ) : (
            /* Desktop version - full progress bar matching SidebarButton height (h-10 = 40px) */
            <div className="w-full h-20 rounded-lg border-gray-200 cursor-pointer relative flex items-center">
              <div className="w-full h-10 rounded-md border-[1px] border-gray-200 cursor-pointer relative overflow-hidden">
                {/* Progress fill with striped pattern */}
                <div
                  className="h-full bg-accent/100 rounded-md transition-all duration-300 relative"
                  style={{ width: '30%' }}
                >
                  {/* Horizontal striped pattern on progress fill */}
                  <div
                    className="absolute inset-0 opacity-20"
                    style={{
                      backgroundImage: `repeating-linear-gradient(
                        45deg,
                        transparent,
                        transparent 2px,
                        rgba(255, 255, 255, 0.3) 2px,
                        rgba(255, 255, 255, 0.3) 4px
                      )`
                    }}
                  />
                </div>

                {/* Horizontal lines with angles on empty space */}
                <div
                  className="absolute inset-0 opacity-30"
                  style={{
                    backgroundImage: `repeating-linear-gradient(
                      45deg,
                      transparent,
                      transparent 3px,
                      rgba(156, 163, 175, 0.4) 3px,
                      rgba(156, 163, 175, 0.4) 6px
                    )`
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Right side - Priorities, Drafts, and Profile */}
        <div className="flex items-center gap-2 h-full">
          {/* Priorities dropdown - moved from sidebar */}
          <DropdownMenu open={isActionDropdownOpen} onOpenChange={setIsActionDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <SidebarButton
                text="P"
                badge={mockActionItems.length}
                variant="ghost"
                layout="horizontal"
                hoverColor="green"
                hoverScale={true}
                showBorder={true}
                className="bg-green-100 hover:bg-green-600 text-green-700 hover:text-white border-green-500 border-2"
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-80 rounded-lg animate-in fade-in-0 zoom-in-95"
              align="end"
              side="bottom"
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-muted-foreground text-xs">
                Project Actions ({mockActionItems.length})
              </DropdownMenuLabel>
              {mockActionItems.map((action) => (
                <DropdownMenuItem
                  key={action.id}
                  className="gap-2 p-3 m-1 border border-transparent transition-colors duration-200 hover:bg-gray-100 hover:border-gray-200 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:border-gray-700 dark:hover:text-gray-100 cursor-pointer rounded-md"
                >
                  <div className="flex size-8 items-center justify-center rounded-md border border-yellow-500 bg-yellow-50">
                    <Zap className={`${ICON_SIZES.md} shrink-0 text-yellow-500`} />
                  </div>
                  <div className="flex flex-col flex-1">
                    <span className="font-medium">{action.priority.toUpperCase()}</span>
                    <span className="text-xs text-muted-foreground capitalize">{action.title}</span>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors duration-200 flex items-center justify-center">
                        <MoreVertical className={ICON_SIZES.md} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-40">
                      <DropdownMenuItem className="gap-2 cursor-pointer hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100">
                        <Eye className={ICON_SIZES.md} />
                        View
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Drafts button */}
          <SidebarButton
            onClick={() => {
              trackClick("drafts-button", "project-header");
              trackCustomEvent("content_section_opened", {
                section: "drafts",
                location: "project-header"
              });
              setActiveContent('drafts');
            }}
            icon={FileText}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            iconClassName={ICON_SIZES.lg}
          />

          {/* Profile button */}
          <SidebarButton
            icon={User}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            onClick={() => {
              trackClick("profile-button", "project-header");
              trackCustomEvent("navigation_clicked", {
                destination: "profile",
                from_page: "project-detail",
                location: "header"
              });
              router.push('/profile');
            }}
            iconClassName={ICON_SIZES.lg}
          />
        </div>
      </div>
    </header>
  );
}

// Mock draft items data
const mockDraftItems = [
  { id: 1, title: "Project proposal", status: "draft", lastModified: "2 hours ago" },
  { id: 2, title: "Design brief", status: "draft", lastModified: "1 day ago" },
  { id: 3, title: "Technical specs", status: "draft", lastModified: "3 days ago" },
];

// Mock file items data
const mockFileItems = [
  { id: 1, title: "logo.svg", type: "image", size: "24KB" },
  { id: 2, title: "wireframes.fig", type: "design", size: "1.2MB" },
  { id: 3, title: "requirements.pdf", type: "document", size: "156KB" },
  { id: 4, title: "styleguide.pdf", type: "document", size: "2.1MB" },
];



export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id;
  const [activeContent, setActiveContent] = useState<'drafts' | 'files' | null>(null);
  const [chatWidth, setChatWidth] = useState<'45%' | '45%'>('45%');
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState('45vw');

  // Sync sidebar width with chat width
    const handleChatWidthChange = (width: '45%' | '45%') => {
    setChatWidth(width);
    setSidebarWidth(width === '45%' ? '45vw' : '45vw');
  };

  const { sections, isLoading, error, setSections, setLoading, setError } = useBusinessSectionStore();
  const { selectedItem, itemDetails, setSelectedItem, setItemDetails } = useBusinessItemStore();

  // Dynamic sidebar width based on selected item
  const currentSidebarWidth = selectedItem ? '30vw' : sidebarWidth;


  // Reset to default view on page load/refresh
  useEffect(() => {
    setSelectedItem(null);
    setItemDetails([]);
  }, [setSelectedItem, setItemDetails]);

  // Load business sections on mount
  useEffect(() => {
    const loadBusinessSections = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchBusinessSections(projectId as string);
        setSections(data);
      } catch (err) {
        setError('Failed to load business sections');
        console.error('Error loading business sections:', err);
      } finally {
        setLoading(false);
      }
    };

    loadBusinessSections();
  }, [setSections, setLoading, setError]);

  // Handle business item selection - show detail view
  const handleBusinessItemClick = (item: any) => {
    setSelectedItem(item);

    // Load mock item details for the selected business item
    const loadItemDetails = async () => {
      try {
        const { fetchItemDetails } = await import('@/stores/businessItemStore');
        const details = await fetchItemDetails(item.id);
        setItemDetails(details);
      } catch (err) {
        console.error('Error loading item details:', err);
        setItemDetails([]);
      }
    };

    loadItemDetails();
  };

  // Handle back to items
  const handleBackToItems = () => {
    setSelectedItem(null);
  };

  // Resize functionality
  const resizable = useResizable({
    initialWidth: sidebarWidth,
    minWidthPercent: 10,
    maxWidthPercent: 70,
    onWidthChange: (width) => {
      // Convert percentage to viewport width
      const widthPercent = parseFloat(width.replace('%', ''));
      const vwWidth = `${widthPercent}vw`;
      setSidebarWidth(vwWidth);

      // Update chatWidth to match sidebarWidth for consistency
      if (widthPercent <= 70) {
        setChatWidth('45%');
      } else {
        setChatWidth('45%');
      }
    },
    onCollapse: () => {
      setIsChatCollapsed(true);
    },
  });

  return (
    <ProtectedRoute>
      <div className="h-screen overflow-hidden">
        <SidebarProvider
          defaultOpen={true}
          style={{
            '--sidebar-width': currentSidebarWidth,
            '--sidebar-width-mobile': '18rem',
            '--sidebar-width-icon': '5rem',
            transition: 'all 0.3s ease-in-out',
          } as React.CSSProperties}
        >
          <ProjectSidebar
            projectId={projectId as string}
            chatWidth={chatWidth}
            setChatWidth={handleChatWidthChange}
            isChatCollapsed={isChatCollapsed}
            setIsChatCollapsed={setIsChatCollapsed}
            selectedBusinessItem={selectedItem}
            showDescription={!!selectedItem}
            onBackToProject={handleBackToItems}
            resizeHandle={{
              onMouseDown: resizable.handleMouseDown,
              isDragging: resizable.isDragging,
            }}
          />
          <SidebarInset className="flex-1 flex flex-col h-screen overflow-hidden">
            <ProjectHeader
              activeContent={activeContent}
              setActiveContent={setActiveContent}
              mockDraftItems={mockDraftItems}
              mockFileItems={mockFileItems}
              selectedBusinessItem={selectedItem}
              onBackToItems={handleBackToItems}
            />

            {/* Main Content Area */}
            <div className="flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative project-main-content transition-all duration-300 ease-in-out">


              {/* Content Section - Drafts */}
              {activeContent === 'drafts' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <SidebarButton
                        icon={X}
                        variant="ghost"
                        size="lg"
                        layout="icon-only"
                        showBorder={true}
                        hoverColor="grey"
                        borderClassName="border-1"
                        hoverScale={true}
                        onClick={() => setActiveContent(null)}
                      >
                      </SidebarButton>
                      <h2 className="text-2xl font-bold text-gray-900">Drafts</h2>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-sm text-gray-500">{mockDraftItems.length} drafts</div>
                      <SidebarButton
                        icon={Plus}
                        variant="ghost"
                        size="lg"
                        showBorder={true}
                        
                        borderClassName="border-1"
                        hoverColor="grey"
                        hoverScale={true}
                      >
                        New Draft
                      </SidebarButton>
                    </div>
                  </div>
                  <div className="grid gap-4">
                    {mockDraftItems.map((draft) => (
                      <div key={draft.id} className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900 text-lg">{draft.title}</h3>
                            <p className="text-sm text-gray-500 mt-1">Modified {draft.lastModified}</p>
                          </div>
                          <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                            {draft.status}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Content Section - Files */}
              {activeContent === 'files' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => setActiveContent(null)}
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                      <h2 className="text-2xl font-bold text-gray-900">Files</h2>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-sm text-gray-500">{mockFileItems.length} files</div>
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        New File
                      </Button>
                    </div>
                  </div>
                  <div className="grid gap-4">
                    {mockFileItems.map((file) => (
                      <div key={file.id} className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                              <span className="text-gray-500 font-semibold text-sm">
                                {file.type === 'image' ? '🖼️' : file.type === 'design' ? '🎨' : '📄'}
                              </span>
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{file.title}</h3>
                              <p className="text-sm text-gray-500 mt-1">{file.size} • {file.type}</p>
                            </div>
                          </div>
                          <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                            {file.type}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <AnimatePresence mode="wait">
                {/* Business Sections Grid - Default View */}
                {!activeContent && !selectedItem && (
                  <motion.div
                    key="business-sections"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    {/* Loading State */}
                    {isLoading && (
                      <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                          <div className={`animate-spin rounded-full ${ICON_SIZES.lg} border-b-2 border-gray-900 mx-auto mb-4`}></div>
                          <p className="text-gray-600">Loading business sections...</p>
                        </div>
                      </div>
                    )}

                    {/* Error State */}
                    {error && (
                      <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                          <p className="text-red-600 mb-4">{error}</p>
                          <Button onClick={() => window.location.reload()}>
                            Try Again
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Business Sections Grid */}
                    {!isLoading && !error && sections.length > 0 && (
                      <BusinessSectionsGrid
                        sections={sections}
                        onItemClick={handleBusinessItemClick}
                      />
                    )}

                    {/* Empty State */}
                    {!isLoading && !error && sections.length === 0 && (
                      <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                          <p className="text-gray-600 mb-4">No business sections found</p>
                          <Button onClick={() => window.location.reload()}>
                            Reload
                          </Button>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}

                {/* Business Item Detail View */}
                {!activeContent && selectedItem && (
                  <motion.div
                    key="business-detail"
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -30 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    <BusinessItemTable
                      itemDetails={itemDetails}
                      selectedBusinessItem={selectedItem}
                      onBackToItems={handleBackToItems}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Bottom fade effect to indicate more content - fixed at bottom of viewport */}
            <div
              className="fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10"
              style={{
                left: `var(--sidebar-width, 45vw)`,
                right: '0'
              }}
            />
            <div
              className="fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10"
              style={{
                left: `var(--sidebar-width, 45vw)`,
                right: '0'
              }}
            />
          </SidebarInset>
        </SidebarProvider>
      </div>
    </ProtectedRoute>
  );
}
