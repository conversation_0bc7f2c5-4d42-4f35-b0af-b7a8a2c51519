"use client";

import { useUser } from "@clerk/nextjs";
import { useState } from "react";
import { toast } from "sonner";

export function useUserUpdate() {
  const { user } = useUser();
  const [isUpdating, setIsUpdating] = useState(false);

  const updateUserInBackend = async (userId: string, userData: {
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    status?: string;
    avatarUrl?: string;
    bio?: string;
    timezone?: string;
    preferences?: any;
  }) => {
    if (!user || isUpdating) return;

    setIsUpdating(true);
    
    try {
      const fullUserData = {
        email: userData.email || user.emailAddresses[0]?.emailAddress || "",
        firstName: userData.firstName || user.firstName || "",
        lastName: userData.lastName || user.lastName || "",
        clerkId: user.id,
        role: userData.role || "user",
        status: userData.status || "active",
        avatarUrl: userData.avatarUrl || user.imageUrl || "",
        bio: userData.bio || "",
        timezone: userData.timezone || "UTC",
        preferences: userData.preferences || {
          notifications: true,
          theme: "system",
          language: "en"
        }
      };

      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      
      console.log("Updating user in backend:", fullUserData);
      console.log("Backend URL:", `${backendUrl}/api/users/${userId}`);

      const response = await fetch(`${backendUrl}/api/users/${userId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(fullUserData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("User successfully updated in backend:", result);
        toast.success("Profile updated successfully!");
        return result;
      } else {
        const errorText = await response.text();
        console.error("Failed to update user in backend:", errorText);
        toast.error(`Update failed: ${response.status} ${response.statusText}`);
        throw new Error(`Update failed: ${response.status}`);
      }
    } catch (error) {
      console.error("Error updating user in backend:", error);
      toast.error("Network error. Please check if your backend is running.");
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const updateUserByClerkId = async (userData: {
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    status?: string;
    avatarUrl?: string;
    bio?: string;
    timezone?: string;
    preferences?: any;
  }) => {
    if (!user || isUpdating) return;

    setIsUpdating(true);
    
    try {
      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3000";
      
      // First, get the user by Clerk ID to find the internal user ID
      const getUserResponse = await fetch(`${backendUrl}/api/users/clerk/${user.id}`);
      
      if (getUserResponse.ok) {
        const existingUser = await getUserResponse.json();
        return await updateUserInBackend(existingUser.id, userData);
      } else {
        console.error("User not found in backend for Clerk ID:", user.id);
        toast.error("User not found in backend. Please sync your account first.");
        throw new Error("User not found in backend");
      }
    } catch (error) {
      console.error("Error updating user by Clerk ID:", error);
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    isUpdating,
    updateUserInBackend,
    updateUserByClerkId,
  };
}
